package main

import (
	"flag"
	"log"

	"indigo/internal/config"
	"indigo/internal/manager"
)

func main() {
	// Parse command line flags
	configFile := flag.String("config", "config.json", "Path to configuration file")
	flag.Parse()

	log.Println("Indigo Voucher Application")

	// Load configuration
	cfg, err := config.LoadConfig(*configFile)
	if err != nil {
		log.Fatal("Failed to load configuration: %s", err)
	}

	log.Printf("🚀 Starting competitive booking bot for %d users", len(cfg.Users))

	// PHASE 1: Pre-authenticate all users (do this BEFORE 7 AM)
	log.Println("📋 Phase 1: Pre-authenticating all users...")

	// Create user manager
	userManager, err := manager.NewUserManager(cfg)
	if err != nil {
		log.Fatal("Failed to create client manager: %s", err)
	}
	// Prepare all users (login, etc.)
	log.Println("Preparing all users...")
	if err := userManager.PrepareAllUsers(); err != nil {
		log.Fatal("Failed to prepare users: %s", err)
	}

	// Wait until target time for all users
	log.Println("Waiting for target time...")
	if err := userManager.WaitForTargetTime(); err != nil {
		log.Fatal("Wait time error: %s", err)
	}

	// Execute spot securing for all users concurrently
	log.Println("Starting spot securing process...")
	userManager.SecureAllSpots()

	// Print results
	userManager.PrintResults()

	log.Println("Application completed successfully")
}
