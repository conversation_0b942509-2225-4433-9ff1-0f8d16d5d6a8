package config

import (
	"encoding/json"
	"fmt"
	"os"
)

// UserConfig holds the configuration for a single user
type UserConfig struct {
	Login     string `json:"login"`
	Password  string `json:"password"`
	Child<PERSON>ear string `json:"child_year"`
	GardenID  string `json:"garden_id"`
	GroupID   string `json:"group_id"`
}

// Config holds the configuration for all users
type Config struct {
	BaseURL      string       `json:"base_url"`
	TargetHour   int          `json:"target_hour"`
	TargetMinute int          `json:"target_minute"`
	TargetSecond int          `json:"target_second"`
	Users        []UserConfig `json:"users"`
}

// LoadConfig loads the configuration from a file with environment variable support
func LoadConfig(filename string) (Config, error) {
	var config Config

	file, err := os.ReadFile(filename)
	if err != nil {
		return config, err
	}

	if err := json.Unmarshal(file, &config); err != nil {
		return config, err
	}

	// Set default target time if not specified
	if config.TargetHour == 0 && config.TargetMinute == 0 && config.TargetSecond == 0 {
		config.TargetHour = 7
		config.TargetMinute = 0
		config.TargetSecond = 0
	}

	// Validate configuration
	if err := config.validate(); err != nil {
		return config, err
	}

	return config, nil
}

// validate performs basic validation of the configuration
func (c *Config) validate() error {
	if len(c.Users) == 0 {
		return fmt.Errorf("no users configured")
	}

	if c.BaseURL == "" {
		return fmt.Errorf("base_url is required")
	}
	if c.TargetHour < 0 || c.TargetHour > 23 {
		return fmt.Errorf("invalid target_hour: %d (must be 0-23)", c.TargetHour)
	}
	if c.TargetMinute < 0 || c.TargetMinute > 59 {
		return fmt.Errorf("invalid target_minute: %d (must be 0-59)", c.TargetMinute)
	}
	if c.TargetSecond < 0 || c.TargetSecond > 59 {
		return fmt.Errorf("invalid target_second: %d (must be 0-59)", c.TargetSecond)
	}

	for i, user := range c.Users {
		if user.Login == "" {
			return fmt.Errorf("user %d: login is required", i+1)
		}
		if user.Password == "" {
			return fmt.Errorf("user %d: password is required", i+1)
		}
		if user.ChildYear == "" {
			return fmt.Errorf("user %d: child_year is required", i+1)
		}
		if user.GardenID == "" {
			return fmt.Errorf("user %d: garden_id is required", i+1)
		}

	}

	return nil
}
