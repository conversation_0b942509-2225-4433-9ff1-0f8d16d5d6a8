package manager

import (
	"context"
	"fmt"
	"log"
	"net"
	"sync"
	"time"

	"github.com/valyala/fasthttp"

	"indigo/internal/client"
	"indigo/internal/config"
	"indigo/internal/utils"
)

// UserManager manages multiple users
type UserManager struct {
	users      []*client.CookieClient
	configs    []config.UserConfig
	results    map[string]string // Track results by client ID
	baseURL    string
	targetTime time.Time
	mutex      sync.Mutex
	wg         sync.WaitGroup
	ctx        context.Context
	cancel     context.CancelFunc
	semaphore  chan struct{} // Semaphore for limiting concurrent requests
}

// NewUserManager creates a new UserManager with the given config
func NewUserManager(cfg config.Config) (*UserManager, error) {
	ctx, cancel := context.WithCancel(context.Background())

	um := &UserManager{
		users:     make([]*client.CookieClient, 0, len(cfg.Users)),
		configs:   make([]config.UserConfig, 0, len(cfg.Users)),
		results:   make(map[string]string),
		baseURL:   cfg.BaseURL,
		ctx:       ctx,
		cancel:    cancel,
		semaphore: make(chan struct{}, 10), // Limit to 10 concurrent requests
	}

	// Set target time
	now := time.Now()
	um.targetTime = time.Date(
		now.Year(), now.Month(), now.Day(),
		cfg.TargetHour, cfg.TargetMinute, cfg.TargetSecond,
		400_000_000, // 400ms
		now.Location(),
	)

	hostClient := &fasthttp.HostClient{
		Addr:                cfg.BaseURL[8:] + ":443",
		IsTLS:               true,
		MaxIdleConnDuration: 300 * time.Second,
		ReadTimeout:         180 * time.Second,
		WriteTimeout:        60 * time.Second,
		// MaxResponseBodySize:           10 * 1024 * 1024,
		DisableHeaderNamesNormalizing: true,
		MaxConns:                      100, // Limit connection pool size
		Dial: func(addr string) (net.Conn, error) {
			return fasthttp.DialTimeout(addr, 20*time.Second)
		},
	}

	for _, clientCfg := range cfg.Users {
		um.configs = append(um.configs, clientCfg)
		um.users = append(um.users, client.NewCookieClient(hostClient))
	}

	return um, nil
}

// PrepareAllusers logs in all users and prepares them for spot securing
func (um *UserManager) PrepareAllUsers() error {
	for i := range um.users {
		um.wg.Add(1)
		go func(index int) {
			defer um.wg.Done()

			c := um.users[index]
			cfg := um.configs[index]
			clientID := fmt.Sprintf("Client-%d-%s", index, cfg.Login)

			// Login
			res, err := client.Login(c, cfg)
			if err != nil {
				um.mutex.Lock()
				um.results[clientID] = fmt.Sprintf("LOGIN ERROR: %s", err)
				um.mutex.Unlock()
				return
			}

			// Submit password
			res, err = client.SubmitPassword(c, res, cfg)
			if err != nil {
				um.mutex.Lock()
				um.results[clientID] = fmt.Sprintf("PASSWORD ERROR: %s", err)
				um.mutex.Unlock()
				fasthttp.ReleaseResponse(res)
				return
			}

			// Send child
			res, err = client.SendChild(c, res, cfg)
			if err != nil {
				um.mutex.Lock()
				um.results[clientID] = fmt.Sprintf("SEND CHILD ERROR: %s", err)
				um.mutex.Unlock()
				fasthttp.ReleaseResponse(res)
				return
			}

			// Fetch group data if needed
			if cfg.GroupID == "" {
				res, err = client.FetchGroupData(c, res, cfg)
				if err != nil {
					um.mutex.Lock()
					um.results[clientID] = fmt.Sprintf("FETCH GROUP ERROR: %s", err)
					um.mutex.Unlock()
					fasthttp.ReleaseResponse(res)
					return
				}
			} else {
				c.SetGroupID(cfg.GroupID)
			}

			fasthttp.ReleaseResponse(res)

			um.mutex.Lock()
			um.results[clientID] = "PREPARED"
			um.mutex.Unlock()
		}(i)
	}

	um.wg.Wait()
	return nil
}

// WaitForTargetTime waits until the target time with millisecond precision
func (um *UserManager) WaitForTargetTime() error {
	if len(um.configs) == 0 {
		return fmt.Errorf("no users configured")
	}

	// Calculate time drift between local and server time
	log.Println("Calculating time drift between local and server time...")
	drift, err := utils.CalculateTimeDrift(um.baseURL+"/ru", 10)
	if err != nil {
		log.Printf("Warning: Failed to calculate time drift: %v. Proceeding with local time.", err)
		drift = 0
	}

	log.Printf("Average time drift: %v", drift)

	// Extract target time components
	targetHour := um.targetTime.Hour()
	targetMin := um.targetTime.Minute()
	targetSec := um.targetTime.Second()
	targetMs := um.targetTime.Nanosecond() / 1000000

	// Wait until the precise target time
	err = utils.WaitUntilPreciseTime(targetHour, targetMin, targetSec, targetMs, drift, um.baseURL)
	if err != nil {
		return fmt.Errorf("failed to wait until target time: %w", err)
	}

	return nil
}

// SecureAllSpots attempts to secure spots for all users concurrently with optimized goroutine management
func (um *UserManager) SecureAllSpots() {
	startTime := time.Now()
	log.Printf("Started securing spots at: %s\n", startTime.Format("15:04:05.000"))

	// Use buffered channel for better goroutine coordination
	startSignal := make(chan struct{})

	// Pre-allocate response objects to reduce GC pressure
	responses := make([]*fasthttp.Response, len(um.users))
	for i := range responses {
		responses[i] = fasthttp.AcquireResponse()
	}

	// Ensure responses are released
	defer func() {
		for _, resp := range responses {
			if resp != nil {
				fasthttp.ReleaseResponse(resp)
			}
		}
	}()

	// Prepare all goroutines but don't start them yet
	for i := range um.users {
		um.wg.Add(1)
		go func(index int) {
			defer um.wg.Done()

			// Acquire semaphore slot
			um.semaphore <- struct{}{}
			defer func() { <-um.semaphore }()

			// Wait for the signal to start
			<-startSignal

			c := um.users[index]
			cfg := um.configs[index]
			clientID := fmt.Sprintf("Client-%d-%s", index, cfg.Login)

			// Skip if client preparation failed
			um.mutex.Lock()
			prepared := um.results[clientID] == "PREPARED"
			um.mutex.Unlock()
			if !prepared {
				return
			}

			// Record the exact start time for this client
			userStartTime := time.Now()

			// Use pre-allocated response
			res := responses[index]
			res.Reset() // Reset for reuse

			// Take place with error handling
			res, err := client.TakePlace(c, res, cfg)
			if err != nil {
				um.updateResult(clientID, fmt.Sprintf("PLACE ERROR: %s", err))
				return
			}

			// Record time after place is taken
			placeTime := time.Now()
			placeDuration := placeTime.Sub(userStartTime)

			// Final step with error handling
			res, err = client.FinalStep(c, res, cfg)
			if err != nil {
				um.updateResult(clientID, fmt.Sprintf("FINAL STEP ERROR: %s", err))
				return
			}

			// Record completion time
			completionTime := time.Now()
			totalDuration := completionTime.Sub(userStartTime)

			// Update results with status
			var resultMsg string
			if res.StatusCode() == fasthttp.StatusOK {
				resultMsg = fmt.Sprintf("SUCCESS - Status: %d, Place: %v, Total: %v",
					res.StatusCode(), placeDuration, totalDuration)
			} else {
				resultMsg = fmt.Sprintf("FAILED - Status: %d, Place: %v, Total: %v",
					res.StatusCode(), placeDuration, totalDuration)
			}
			um.updateResult(clientID, resultMsg)
		}(i)
	}

	// Signal all goroutines to start simultaneously
	close(startSignal)

	um.wg.Wait()
	endTime := time.Now()
	log.Printf("Finished securing spots at: %s (took %s)\n",
		endTime.Format("15:04:05.000"),
		endTime.Sub(startTime))
}

// updateResult safely updates the result for a client
func (um *UserManager) updateResult(clientID, result string) {
	um.mutex.Lock()
	um.results[clientID] = result
	um.mutex.Unlock()
}

// GetBaseURL returns the base URL for all clients
func (um *UserManager) GetBaseURL() string {
	return um.config.BaseURL
}

// PrintResults prints the results for all users
func (um *UserManager) PrintResults() {
	fmt.Println("\n--- RESULTS ---")
	for i, cfg := range um.configs {
		clientID := fmt.Sprintf("Client-%d-%s", i, cfg.Login)
		result := um.results[clientID]
		fmt.Printf("Client %d (%s): %s\n", i+1, cfg.Login, result)
	}
}
