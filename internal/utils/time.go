package utils

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"
)

// TimeData represents the server time
type TimeData struct {
	H   int `json:"h"`
	Min int `json:"min"`
	S   int `json:"s"`
}

// GetServerTime gets the current server time with high precision
func GetServerTime(baseURL string) (time.Time, error) {
	startTime := time.Now()
	resp, err := http.Get(baseURL + "/gettime")
	if err != nil {
		return time.Time{}, fmt.Errorf("failed to get server time: %w", err)
	}
	defer resp.Body.Close()

	endTime := time.Now()
	roundTripTime := endTime.Sub(startTime)

	var timeData TimeData
	if err := json.NewDecoder(resp.Body).Decode(&timeData); err != nil {
		return time.Time{}, fmt.Errorf("failed to decode JSON: %w", err)
	}

	now := time.Now()

	// Estimate one-way latency (half of round trip)
	latency := roundTripTime / 2

	// Create server time
	serverTime := time.Date(
		now.Year(), now.Month(), now.Day(),
		timeData.H, timeData.Min, timeData.S,
		0, // Start with 0 nanoseconds
		now.Location(),
	)

	// Adjust for network latency
	adjustedTime := serverTime.Add(latency)

	return adjustedTime, nil
}

// CalculateTimeDrift calculates the average time drift between local and server time
func CalculateTimeDrift(baseURL string, samples int) (time.Duration, error) {
	var totalDrift time.Duration
	successfulSamples := 0

	for i := 0; i < samples; i++ {
		localBefore := time.Now()
		serverTime, err := GetServerTime(baseURL)
		localAfter := time.Now()

		if err != nil {
			log.Printf("Sample %d failed: %v", i+1, err)
			continue
		}

		// Calculate midpoint of local time
		localMid := localBefore.Add(localAfter.Sub(localBefore) / 2)

		// Calculate drift
		drift := serverTime.Sub(localMid)
		totalDrift += drift
		successfulSamples++

		log.Printf("Sample %d: Local: %v, Server: %v, Drift: %v",
			i+1, localMid.Format("15:04:05.000"),
			serverTime.Format("15:04:05.000"), drift)

		// Wait a bit between samples
		time.Sleep(100 * time.Millisecond)
	}

	if successfulSamples == 0 {
		return 0, fmt.Errorf("all time samples failed")
	}

	averageDrift := totalDrift / time.Duration(successfulSamples)
	return averageDrift, nil
}

// WaitUntilPreciseTime waits until the exact target time with millisecond precision
func WaitUntilPreciseTime(targetHour, targetMin, targetSec, targetMs int, drift time.Duration, baseURL string) error {
	// Get current time
	now := time.Now()

	// Create target time
	targetTime := time.Date(
		now.Year(), now.Month(), now.Day(),
		targetHour, targetMin, targetSec,
		targetMs*1000000, // Convert ms to ns
		now.Location(),
	)

	// Adjust target time based on calculated drift
	adjustedTargetTime := targetTime.Add(-drift)

	// Calculate time to wait
	timeToWait := adjustedTargetTime.Sub(now)

	log.Printf("Target time: %v", targetTime.Format("15:04:05.000"))
	log.Printf("Adjusted target time (accounting for drift): %v", adjustedTargetTime.Format("15:04:05.000"))
	log.Printf("Current time: %v", now.Format("15:04:05.000"))
	log.Printf("Time to wait: %v", timeToWait)

	if timeToWait <= 0 {
		log.Printf("Target time already passed by %v", -timeToWait)
		return nil
	}

	// Sleep until 50ms before the target time
	sleepTime := timeToWait - 50*time.Millisecond
	if sleepTime > 0 {
		log.Printf("Sleeping for %v", sleepTime)
		time.Sleep(sleepTime)
	}

	// Busy wait for the final approach to get precise timing
	log.Printf("Starting busy wait at %v", time.Now().Format("15:04:05.000"))
	for time.Now().Before(adjustedTargetTime) {
		// Busy wait with minimal sleep to reduce CPU usage
		time.Sleep(time.Microsecond)
	}

	log.Printf("Finished waiting at %v", time.Now().Format("15:04:05.000"))

	// Verify timing accuracy
	actualTime := time.Now()
	accuracy := actualTime.Sub(adjustedTargetTime)
	log.Printf("Timing accuracy: %v", accuracy)

	return nil
}
