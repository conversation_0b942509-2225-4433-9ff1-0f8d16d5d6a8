package utils

import (
	"fmt"
	"time"

	"github.com/valyala/fasthttp"
)

const (
	contentType  = "application/x-www-form-urlencoded"
	acceptHeader = "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"
)

// NewGetRequest creates a new GET request
func NewGetRequest(url string) *fasthttp.Request {
	req := fasthttp.AcquireRequest()
	req.SetRequestURI(url)
	req.Header.SetMethod(fasthttp.MethodGet)
	req.Header.SetUserAgent(GetUserAgent())
	req.Header.Set("Accept", acceptHeader)
	req.Header.SetContentType(contentType)
	req.Header.Set("Cache-Control", "no-cache")
	req.Header.Set("Pragma", "no-cache")

	// Add a unique ID to prevent caching
	req.URI().QueryArgs().Add("_", time.Now().Format(time.RFC3339Nano))

	return req
}

// NewPostRequest creates a new POST request
func NewPostRequest(url string, formData map[string]string) *fasthttp.Request {
	req := fasthttp.AcquireRequest()
	req.SetRequestURI(url)
	req.Header.SetMethod(fasthttp.MethodPost)
	req.Header.SetUserAgent(GetUserAgent())
	req.Header.Set("Accept", acceptHeader)
	req.Header.SetContentType(contentType)
	req.Header.Set("Cache-Control", "no-cache")
	req.Header.Set("Pragma", "no-cache")

	// Build form data
	args := fasthttp.AcquireArgs()
	for key, value := range formData {
		args.Add(key, value)
	}

	// Add a unique ID to prevent caching
	args.Add("unique_id", time.Now().Format(time.RFC3339Nano))

	args.WriteTo(req.BodyWriter())
	fasthttp.ReleaseArgs(args)

	return req
}

// OptimizeRequest optimizes a request for speed
func OptimizeRequest(req *fasthttp.Request) {
	// Disable compression to reduce processing time
	req.Header.Del("Accept-Encoding")

	// Disable keep-alive to ensure a fresh connection
	req.Header.Set("Connection", "close")

	// Add high priority headers
	req.Header.Set("Priority", "u=1")

	// Add cache busting
	req.URI().QueryArgs().Add("_nocache", fmt.Sprintf("%d", time.Now().UnixNano()))

	// Set a short timeout
	req.Header.Set("X-Request-Timeout", "5000")

	// Reduce data transfer by requesting minimal content
	req.Header.Set("X-Requested-With", "XMLHttpRequest")

	// Indicate preference for reduced content
	req.Header.Set("Save-Data", "on")
}
