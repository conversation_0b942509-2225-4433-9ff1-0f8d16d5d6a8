package utils

import (
	"bytes"
	"crypto/md5"
	"encoding/hex"
	"math/rand"
	"time"
)

var userAgent string

func init() {
	userAgents := []string{
		"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
		"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.1.15",
		"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.111 Safari/537.36",
		"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5 Mobile/15E148 Safari/604.1",
	}
	source := rand.NewSource(time.Now().UnixNano())
	r := rand.New(source)
	userAgent = userAgents[r.Intn(len(userAgents))]
}

// GetUserAgent returns a random user agent
func GetUserAgent() string {
	return userAgent
}

// ExtractCSRFToken extracts the CSRF token from HTML
func ExtractCSRFToken(htmlBody []byte) string {
	csrfStart := bytes.Index(htmlBody, []byte(`name="_csrf-frontend"`))
	if csrfStart == -1 {
		return ""
	}
	valueStart := bytes.Index(htmlBody[csrfStart:], []byte(`value="`))
	if valueStart == -1 {
		return ""
	}
	valueStart += csrfStart + len(`value="`)
	valueEnd := bytes.Index(htmlBody[valueStart:], []byte(`"`))
	if valueEnd == -1 {
		return ""
	}
	csrfToken := htmlBody[valueStart : valueStart+valueEnd]
	return string(csrfToken)
}

// ExtractChildData extracts child data from HTML
func ExtractChildData(iinPrefix, html []byte) (string, string) {
	// The key to search for the iin value
	iinKey := []byte(`name="ReserveForm[iin]" value="`)

	// The key to search for the request number value
	reqNumKey := []byte(`name="ReserveForm[requestNumber]" value="`)

	var iin, requestNumber []byte
	var startIndex, endIndex int

	for {
		// Find the iin field by searching the pattern `ReserveForm[iin]`
		startIndex = bytes.Index(html, iinKey)
		if startIndex == -1 {
			// No more iin fields found
			break
		}

		// Extract the iin value
		startIndex += len(iinKey)
		endIndex = bytes.IndexByte(html[startIndex:], '"') + startIndex
		iin = html[startIndex:endIndex]

		// Check if iin starts with the required prefix
		if bytes.HasPrefix(iin, iinPrefix) {
			// If the iin matches, search for the corresponding request number
			reqStartIndex := bytes.Index(html[endIndex:], reqNumKey)
			if reqStartIndex != -1 {
				// Extract the requestNumber value
				reqStartIndex += endIndex + len(reqNumKey)
				reqEndIndex := bytes.IndexByte(html[reqStartIndex:], '"') + reqStartIndex
				requestNumber = html[reqStartIndex:reqEndIndex]
				return string(iin), string(requestNumber)
			}
		}

		// Move past the current `ReserveForm[iin]` field to continue searching
		html = html[endIndex:]
	}
	return "", ""
}

// ExtractGroupID extracts the group ID from HTML
func ExtractGroupID(html []byte) (string, int) {
	data := []byte(`name="ReserveForm[groupId]" value="`)
	var endIndex int
	startIndex := bytes.Index(html, data)
	if startIndex == -1 {
		return "", startIndex
	} else {
		startIndex += len(data)
		endIndex = bytes.IndexByte(html[startIndex:], '"') + startIndex
		data = html[startIndex:endIndex]
	}
	return string(data), endIndex
}

// ExtractFinalPage extracts data from the final page
func ExtractFinalPage(html []byte) map[string]string {
	fields := []struct {
		key   string
		query []byte
	}{
		{"name", []byte(`name="ReservationForm[name]" value="`)},
		{"kindergarden_type", []byte(`name="ReservationForm[kindergarden_type]" value="`)},
		{"group_name", []byte(`name="ReservationForm[group_name]" value="`)},
		{"group_type", []byte(`name="ReservationForm[group_type]" value="`)},
		{"age_group", []byte(`name="ReservationForm[age_group]" value="`)},
		{"study_language", []byte(`name="ReservationForm[study_language]" value="`)},
		{"iin", []byte(`name="ReservationForm[iin]" value="`)},
		{"groupId", []byte(`name="ReservationForm[groupId]" value="`)},
		{"requestNumber", []byte(`name="ReservationForm[requestNumber]" value="`)},
		{"finalHash", []byte(`name="ReserveForm[finalHash]" value="`)},
	}

	result := make(map[string]string)
	var startIndex, endIndex int

	for _, field := range fields {
		startIndex = bytes.Index(html, field.query)
		if startIndex == -1 {
			return nil
		}

		startIndex += len(field.query)
		endIndex = bytes.IndexByte(html[startIndex:], '"') + startIndex
		result[field.key] = string(html[startIndex:endIndex])
	}

	return result
}

// GenerateMD5 generates an MD5 hash of the input string
func GenerateMD5(inputStr string) string {
	hash := md5.Sum([]byte(inputStr))
	return hex.EncodeToString(hash[:])
}
