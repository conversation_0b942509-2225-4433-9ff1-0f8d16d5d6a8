package utils

import (
	"fmt"
	"time"
)

// TestTimingPrecision tests the precision of time.Sleep on the current system
func TestTimingPrecision() {
	fmt.Println("Testing sleep precision for different durations...")

	// Test different sleep durations
	durations := []time.Duration{
		1 * time.Millisecond,
		5 * time.Millisecond,
		10 * time.Millisecond,
		50 * time.Millisecond,
		100 * time.Millisecond,
	}

	for _, targetDuration := range durations {
		var totalError time.Duration
		var maxError time.Duration
		samples := 10

		fmt.Printf("\nTesting %v sleep precision:\n", targetDuration)

		for i := 0; i < samples; i++ {
			start := time.Now()
			time.Sleep(targetDuration)
			actualDuration := time.Since(start)
			error := actualDuration - targetDuration

			if error > maxError {
				maxError = error
			}

			totalError += error
			fmt.Printf("  Sample %d: Target=%v, Actual=%v, Error=%v\n",
				i+1, targetDuration, actualDuration, error)
		}

		avgError := totalError / time.Duration(samples)
		fmt.Printf("  Average error: %v\n", avgError)
		fmt.Printf("  Maximum error: %v\n", maxError)

		// Calculate error percentage
		errorPercentage := float64(avgError) / float64(targetDuration) * 100

		if errorPercentage < 10 {
			fmt.Printf("  ✅ EXCELLENT: Average error is <10%% of target duration\n")
		} else if errorPercentage < 50 {
			fmt.Printf("  ✅ GOOD: Average error is <50%% of target duration\n")
		} else if errorPercentage < 100 {
			fmt.Printf("  ⚠️ FAIR: Average error is <100%% of target duration\n")
		} else {
			fmt.Printf("  ❌ POOR: Average error is >100%% of target duration\n")
		}
	}

	// Test busy waiting precision
	fmt.Println("\nTesting busy waiting precision:")
	targetDuration := 50 * time.Millisecond

	start := time.Now()
	deadline := start.Add(targetDuration)

	// Busy wait
	for time.Now().Before(deadline) {
		// Busy wait with minimal sleep to reduce CPU usage
		time.Sleep(time.Microsecond)
	}

	actualDuration := time.Since(start)
	error := actualDuration - targetDuration

	fmt.Printf("  Busy wait: Target=%v, Actual=%v, Error=%v\n",
		targetDuration, actualDuration, error)

	// Calculate error percentage
	errorPercentage := float64(error) / float64(targetDuration) * 100

	if errorPercentage < 1 {
		fmt.Printf("  ✅ EXCELLENT: Busy wait error is <1%% of target duration\n")
	} else if errorPercentage < 5 {
		fmt.Printf("  ✅ GOOD: Busy wait error is <5%% of target duration\n")
	} else if errorPercentage < 10 {
		fmt.Printf("  ⚠️ FAIR: Busy wait error is <10%% of target duration\n")
	} else {
		fmt.Printf("  ❌ POOR: Busy wait error is >10%% of target duration\n")
	}

	// Recommendations
	fmt.Println("\nRecommendations based on timing tests:")

	if errorPercentage < 5 {
		fmt.Println("1. Your system has excellent timing precision. You can rely on the busy wait approach.")
		fmt.Println("2. For critical operations, use busy waiting for the final approach to the target time.")
	} else {
		fmt.Println("1. Your system has limited timing precision. Consider using a different machine.")
		fmt.Println("2. Try running with higher process priority or real-time scheduling if available.")
		fmt.Println("3. Close other applications that might be consuming CPU resources.")
	}
}
